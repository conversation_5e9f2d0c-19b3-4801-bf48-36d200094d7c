namespace Esky.FlightsCache.MessageContract
{
    public class PriceCacheEntry
    {
        public PriceCacheEntry() { }

        public PriceCacheEntry(decimal basePrice, decimal taxPrice, int minimumNumberOPaxes = 1, decimal? marginIncluded = null, decimal? packagesAdditionalMargin = null)
        {
            BasePrice = basePrice;
            TaxPrice = taxPrice;
            MinimumNumberOfPaxes = minimumNumberOPaxes;
            MarginIncluded = marginIncluded;
            PackagesAdditionalMargin = packagesAdditionalMargin;
        }

        public decimal BasePrice { get; set; }
        public decimal TaxPrice { get; set; }
        public int MinimumNumberOfPaxes { get; set; }
        internal bool IsGenerated { get; set; } = false;
        
        public decimal? MarginIncluded { get; set; }
        public decimal? PackagesAdditionalMargin { get; set; }
    }
}