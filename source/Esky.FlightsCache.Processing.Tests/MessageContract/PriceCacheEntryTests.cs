using Esky.FlightsCache.MessageContract;
using FluentAssertions;

namespace Esky.FlightsCache.Processing.Tests.MessageContract;

public class PriceCacheEntryTests
{
    [Fact]
    public void OperatorPlus_ShouldAccumulateAllPriceComponents()
    {
        // Arrange
        var left = new PriceCacheEntry(100, 20, 1, marginIncluded: 5, packagesAdditionalMargin: 2);
        var right = new PriceCacheEntry(80, 10, 2, marginIncluded: 3, packagesAdditionalMargin: 1);

        // Act
        var result = left + right;

        // Assert
        result.BasePrice.Should().Be(180); // 100 + 80
        result.TaxPrice.Should().Be(30); // 20 + 10
        result.MinimumNumberOfPaxes.Should().Be(1); // From left operand
        result.MarginIncluded.Should().Be(8); // 5 + 3
        result.PackagesAdditionalMargin.Should().Be(3); // 2 + 1
        result.IsGenerated.Should().BeFalse(); // Both false
    }

    [Fact]
    public void OperatorPlus_ShouldHandleNullMargins()
    {
        // Arrange
        var left = new PriceCacheEntry(100, 20, 1, marginIncluded: null, packagesAdditionalMargin: 2);
        var right = new PriceCacheEntry(80, 10, 2, marginIncluded: 3, packagesAdditionalMargin: null);

        // Act
        var result = left + right;

        // Assert
        result.BasePrice.Should().Be(180);
        result.TaxPrice.Should().Be(30);
        result.MarginIncluded.Should().Be(3); // 0 + 3
        result.PackagesAdditionalMargin.Should().Be(2); // 2 + 0
    }

    [Fact]
    public void OperatorPlus_ShouldPreserveIsGeneratedFlag()
    {
        // Arrange
        var left = new PriceCacheEntry(100, 20, 1) { IsGenerated = true };
        var right = new PriceCacheEntry(80, 10, 2) { IsGenerated = false };

        // Act
        var result = left + right;

        // Assert
        result.IsGenerated.Should().BeTrue(); // true OR false = true
    }

    [Fact]
    public void OperatorPlus_ShouldHandleNullOperands()
    {
        // Arrange
        var entry = new PriceCacheEntry(100, 20, 1, marginIncluded: 5, packagesAdditionalMargin: 2);

        // Act & Assert
        var result1 = entry + null;
        result1.Should().Be(entry);

        var result2 = null + entry;
        result2.Should().Be(entry);

        var result3 = (PriceCacheEntry)null + (PriceCacheEntry)null;
        result3.Should().BeNull();
    }

    [Fact]
    public void Zero_ShouldCreateEntryWithZeroValues()
    {
        // Act
        var result = PriceCacheEntry.Zero(5);

        // Assert
        result.BasePrice.Should().Be(0);
        result.TaxPrice.Should().Be(0);
        result.MinimumNumberOfPaxes.Should().Be(5);
        result.MarginIncluded.Should().Be(0);
        result.PackagesAdditionalMargin.Should().Be(0);
        result.IsGenerated.Should().BeFalse();
    }

    [Fact]
    public void Zero_ShouldUseDefaultMinimumNumberOfPaxes()
    {
        // Act
        var result = PriceCacheEntry.Zero();

        // Assert
        result.MinimumNumberOfPaxes.Should().Be(1);
    }
}
