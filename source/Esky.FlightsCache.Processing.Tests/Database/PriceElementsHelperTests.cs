using Esky.FlightsCache.Database.Helpers;
using Esky.FlightsCache.MessageContract;
using System.Collections.Generic;

namespace Esky.FlightsCache.Processing.Tests.Database;

public class PriceElementsHelperTests
{
    [Fact]
    public void Create_ShouldReturnEmptyDictionary_WhenNoPricesProvided()
    {
        var outboundLeg = new FlightCacheLeg { AdultPrices = [] };
        var inboundLeg = new FlightCacheLeg { AdultPrices = [] };

        var result = PriceElementsHelper.Create(outboundLeg, inboundLeg);

        result.Should().BeEmpty();
    }

    [Fact]
    public void Create_ShouldReturnOutboundPrices_WhenNoInboundPricesProvided()
    {
        var outboundLeg = new FlightCacheLeg
        {
            AdultPrices =
            [
                new PriceCacheEntry(100, 20, 1),
                new PriceCacheEntry(90, 15, 2)
            ]
        };

        var result = PriceElementsHelper.Create(outboundLeg, null);

        result.Should().ContainKey("A");
        result["A"].Base.Should().Be(100);
        result["A"].Tax.Should().Be(20);
        result.Should().ContainKey("A2");
        result["A2"].Base.Should().Be(90);
        result["A2"].Tax.Should().Be(15);
    }

    [Fact]
    public void Create_ShouldReturnInboundPrices_WhenNoOutboundPricesProvided()
    {
        var inboundLeg = new FlightCacheLeg
        {
            AdultPrices =
            [
                new PriceCacheEntry(80, 10, 1),
                new PriceCacheEntry(70, 5, 2)
            ]
        };

        var result = PriceElementsHelper.Create(null, inboundLeg);

        result.Should().ContainKey("A");
        result["A"].Base.Should().Be(80);
        result["A"].Tax.Should().Be(10);
        result.Should().ContainKey("A2");
        result["A2"].Base.Should().Be(70);
        result["A2"].Tax.Should().Be(5);
    }

    [Fact]
    public void Create_ShouldMergeOutboundAndInboundPrices()
    {
        var outboundLeg = new FlightCacheLeg
        {
            AdultPrices =
            [
                new PriceCacheEntry(100, 20, 1),
                new PriceCacheEntry(90, 15, 2)
            ]
        };

        var inboundLeg = new FlightCacheLeg
        {
            AdultPrices =
            [
                new PriceCacheEntry(80, 10, 1),
                new PriceCacheEntry(70, 5, 2)
            ]
        };

        var result = PriceElementsHelper.Create(outboundLeg, inboundLeg);

        result.Should().ContainKey("A");
        result["A"].Base.Should().Be(180);
        result["A"].Tax.Should().Be(30);
        result.Should().ContainKey("A2");
        result["A2"].Base.Should().Be(160);
        result["A2"].Tax.Should().Be(20);
    }

    [Fact]
    public void Create_ShouldHandleMixedPriceTypes()
    {
        var outboundLeg = new FlightCacheLeg
        {
            AdultPrices = [new PriceCacheEntry(100, 20, 1)],
            ChildPrices = [new PriceCacheEntry(50, 10, 1)]
        };

        var inboundLeg = new FlightCacheLeg
        {
            AdultPrices = [new PriceCacheEntry(80, 10, 1)],
            ChildPrices = [new PriceCacheEntry(40, 5, 1)]
        };

        var result = PriceElementsHelper.Create(outboundLeg, inboundLeg);

        result.Should().ContainKey("A");
        result["A"].Base.Should().Be(180);
        result["A"].Tax.Should().Be(30);
        result.Should().ContainKey("C");
        result["C"].Base.Should().Be(90);
        result["C"].Tax.Should().Be(15);
    }

    [Fact]
    public void Create_ShouldHandleRawPriceComponents_WhenNoInboundLeg()
    {
        var outboundLeg = new FlightCacheLeg
        {
            AdultPrices = [new PriceCacheEntry(100, 20, 1)],
            RawPriceComponents = new Dictionary<string, PriceCacheEntry>
            {
                { "_R1", new PriceCacheEntry(50, 5, 1) }
            }
        };

        var result = PriceElementsHelper.Create(outboundLeg, null);

        result.Should().ContainKey("A");
        result["A"].Base.Should().Be(100);
        result["A"].Tax.Should().Be(20);
        result.Should().ContainKey("_R1");
        result["_R1"].Base.Should().Be(50);
        result["_R1"].Tax.Should().Be(5);
    }
}