using Esky.FlightsCache.Database.FlightOffers.Model;
using Esky.FlightsCache.MessageContract;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Esky.FlightsCache.Database.Helpers;

public static class PriceElementsHelper
{
    private const string _priceForAdult = "A";
    private const string _priceForYouth = "Y";
    private const string _priceForChild = "C";
    private const string _priceForInfant = "I";

    public static Dictionary<string, PriceElement> Create(FlightCacheLeg outboundLeg, FlightCacheLeg inboundLeg)
    {
        var adultPrices = GetPrices(_priceForAdult, outboundLeg?.AdultPrices, inboundLeg?.AdultPrices);
        var youthPrices = GetPrices(_priceForYouth, outboundLeg?.YouthPrices, inboundLeg?.YouthPrices);
        var childPrices = GetPrices(_priceForChild, outboundLeg?.ChildPrices, inboundLeg?.ChildPrices);
        var infantPrices = GetPrices(_priceForInfant, outboundLeg?.InfantPrices, inboundLeg?.InfantPrices);

        var result = adultPrices.Concat(youthPrices).Concat(childPrices).Concat(infantPrices);

        if (inboundLeg == null)
        {
            var rawPriceComponents = outboundLeg?.RawPriceComponents?
                .Select(x => new { x.Key, PriceElement = new PriceElement { Base = x.Value.BasePrice, Tax = x.Value.TaxPrice } })
                .ToDictionary(x => x.Key, x => x.PriceElement);
            if(rawPriceComponents is not null) result = result.Concat(rawPriceComponents);
        }

        return result.ToDictionary();
    }

    private static Dictionary<string, PriceElement> GetPrices(string key, List<PriceCacheEntry> outboundPrices, List<PriceCacheEntry> inboundPrices)
    {
        if (outboundPrices == null || outboundPrices.Count == 0)
        {
            if (inboundPrices == null || inboundPrices.Count == 0)
            {
                return new Dictionary<string, PriceElement>();
            }

            return AddPricesToDictionary(key, inboundPrices);
        }

        if (inboundPrices == null || inboundPrices.Count == 0)
        {
            return AddPricesToDictionary(key, outboundPrices);
        }

        var mergedPrices = new List<PriceCacheEntry>();
        using var outboundEnumerator = outboundPrices.OrderByDescending(x => x.MinimumNumberOfPaxes).GetEnumerator();
        using var inboundEnumerator = inboundPrices.OrderByDescending(x => x.MinimumNumberOfPaxes).GetEnumerator();
        var maxPaxesCount = Math.Max(outboundPrices.Max(x => x.MinimumNumberOfPaxes), inboundPrices.Max(x => x.MinimumNumberOfPaxes));
        maxPaxesCount = Math.Max(maxPaxesCount, 1);

        outboundEnumerator.MoveNext();
        inboundEnumerator.MoveNext();

        var shouldSaveResult = true;

        for (var i = maxPaxesCount; i >= 1; i--)
        {
            if (outboundEnumerator.Current != null && outboundEnumerator.Current.MinimumNumberOfPaxes > i)
            {
                shouldSaveResult = true;
                outboundEnumerator.MoveNext();
            }

            if (inboundEnumerator.Current != null && inboundEnumerator.Current.MinimumNumberOfPaxes > i)
            {
                shouldSaveResult = true;
                inboundEnumerator.MoveNext();
            }

            if (shouldSaveResult)
            {
                shouldSaveResult = false;

                var basePrice = 0M;
                var taxPrice = 0M;

                if (outboundEnumerator.Current != null)
                {
                    basePrice += outboundEnumerator.Current.MinimumNumberOfPaxes <= i ? outboundEnumerator.Current.BasePrice : 0M;
                    taxPrice += outboundEnumerator.Current.MinimumNumberOfPaxes <= i ? outboundEnumerator.Current.TaxPrice : 0M;
                }

                if (inboundEnumerator.Current != null)
                {
                    basePrice += inboundEnumerator.Current.MinimumNumberOfPaxes <= i ? inboundEnumerator.Current.BasePrice : 0M;
                    taxPrice += inboundEnumerator.Current.MinimumNumberOfPaxes <= i ? inboundEnumerator.Current.TaxPrice : 0M;
                }

                if (basePrice == 0M && taxPrice == 0M)
                {
                    continue;
                }

                mergedPrices.Add(new PriceCacheEntry(basePrice, taxPrice, i));
            }
        }

        return AddPricesToDictionary(key, mergedPrices.OrderBy(x => x.MinimumNumberOfPaxes).ToList());
    }

    private static Dictionary<string, PriceElement> AddPricesToDictionary(string key, IList<PriceCacheEntry> prices)
    {
        var result = new Dictionary<string, PriceElement>();

        if (prices == null || !prices.Any())
        {
            return result;
        }

        var orderedPrices = prices.OrderBy(x => x.MinimumNumberOfPaxes).ToList();

        // Create first price element with pax code, eg. 'A'
        result[key] = new PriceElement { Base = orderedPrices.First().BasePrice, Tax = orderedPrices.First().TaxPrice };

        // Next elements will have pax code with minimum number of paxes suffix, eg. 'A2', 'C5'
        foreach (var price in orderedPrices.Skip(1))
        {
            result[key + price.MinimumNumberOfPaxes] = new PriceElement { Base = price.BasePrice, Tax = price.TaxPrice };
        }

        return result;
    }
}